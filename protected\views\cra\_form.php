<style>
/* Custom styling for exposition dropdown with optgroups */
#exposition optgroup {
    font-weight: bold;
    font-style: normal;
    background-color: #f5f5f5;
    color: #333;
}

#exposition option {
    font-weight: normal;
    padding-left: 20px;
}

#exposition optgroup option {
    padding-left: 20px;
}

/* Fix form alignment in process mode */
.form-group .col-sm-3.control-label {
    text-align: right;
    padding-top: 7px;
    margin-bottom: 0;
    font-weight: bold;
}

.form-group .col-sm-9 {
    padding-left: 15px;
}

/* Ensure proper clearfix for form groups */
.form-group:after {
    content: "";
    display: table;
    clear: both;
}
</style>

<div class="row">
	<div class="col-md-12">
		<div class="panel panel-default keyvisual">
			<div class="panel-body">
				<!-- Hidden field for request IDs (API integration) -->
				<input type="hidden" name="requestIds" id="requestIds" value="">
				<!-- Hidden field for action type (save, save-submit) -->
				<input type="hidden" name="typeAction" id="typeAction" value="">

				<div class="form-group">
					<div class="col-sm-6" style="padding: 0;">
						<label class="col-sm-2 control-label" for="ProductCode">Requests</label>
						<div class="col-sm-10">
							<input type="text" placeholder="Request number" id="requestId" class="form-control" value="" style="display: inline-block;">
							<?php
								if(!isset($requests)){
									$requests = array();
								}
							?>
						</div>
					</div>
					<div class="col-sm-6" style="padding: 0;">
						<button class="btn btn-primary" id="add-request-number" style="margin-bottom: 4px;">Add</button>
					</div>
				</div>
				<div class="form-group table-request-info" style="<?php if(sizeof($requests) == 0) echo 'display:none;'?>">
					<label class="col-sm-1 control-label" for="ProductCode"></label>
					<div class="col-sm-11">
						<div class="table-responsive" style="<?php if(sizeof($requests) == 0) echo 'display:none;'?>">
							<table class="table table-editable table-hover table-striped table-bordered no-margin">
								<thead class="text-nowrap">
									<tr>
										<th>Request No</th>
										<th>Brand Name</th>
										<th>Product Name</th>
										<th>DAV Notification Number</th>
										<th>Date of receiving from DAV</th>
										<th>Date of expiring DAV</th>
										<th></th>
									</tr>
								</thead>
								<tbody id="list-requests">
									<?php foreach($requests as $index=>$request):?>
									<tr>
										<td class="request id"><?php echo isset($request['id'])?$request['id']:"";?></td>
										<td class="request brandName"><?php echo isset($request['brandName'])?$request['brandName']:"";?></td>
										<td class="request productName"><?php echo isset($request['productName'])?$request['productName']:"";?></td>
										<td class="request davNotificationNumber"><?php echo isset($request['davNotificationNumber'])?$request['davNotificationNumber']:"";?></td>
										<td class="request davReceivingDate"><?php echo isset($request['davReceivingDate'])?$request['davReceivingDate']:"";?></td>
										<td class="request davExpiringDate"><?php echo isset($request['davExpiringDate'])?$request['davExpiringDate']:"";?></td>
										<td class="text-center">
											<button class="btn btn-default no-border btn-sm remove-request"><i class="fa fa-minus"></i></button>
										</td>
									</tr>
									<?php endforeach;?>
								</tbody>
							</table>
						</div>
					</div>
				</div>

				<div class="form-group">
					<div class="col-sm-6" style="padding: 0;<?php
						// Hide for process mode since it's now in PROCESSING INFORMATION panel
						if (isset($typeForm) && $typeForm == 'process') {
							echo "display: none";
						} else if (in_array(Yii::app()->user->groupName, array("SCI","SCI Manager","SCI Staff"))) {
							echo "display: block";
						} else {
							echo "display: none";
						}
					?>">
						<label class="col-sm-2 control-label" for="newProofDocuments">Proof Documents</label>
						<div class="col-sm-10">
							<input type="file" class="newProofDocuments input-keyvisual inline-block" name="newProofDocuments[]" multiple="multiple">
							<?php if(isset($item) && isset($item->proofDocuments) && sizeof((array)$item->proofDocuments)>0):?>
							<br>
							<div class="upload-file table-responsive">
								<table class="currentProofDocuments table table-editable table-hover table-striped table-bordered no-margin">
									<thead class="text-nowrap">
										<tr>
											<th class="limit-width">Filename</th>
											<th>Tools</th>
										</tr>
									</thead>
									<tbody>
										<?php foreach($item->proofDocuments as $file_id=>$file_name):?>
										<tr id="<?php echo $file_id;?>">
											<td class="limit-width">
												<?php echo $file_name;?>
											</td>
											<td class="text-center">
												<button class="btn btn-default no-border btn-sm download-file">
													<i class="fa fa-download"></i>
												</button>
												<button class="btn btn-default no-border btn-sm remove-file">
													<i class="fa fa-remove"></i>
												</button>
											</td>
										</tr>
										<?php endforeach;?>
									</tbody>
								</table>
							</div>
							<?php endif;?>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="col-md-6">
		<div class="panel panel-default">
			<div class="panel-body">
				<!-- Exposition (required) -->
				<div class="form-group">
					<label class="col-sm-3 control-label" for="exposition">Exposition</label>
					<div class="col-sm-9">
						<select name="exposition" id="exposition" class="form-control" required>
							<option value="">Select Exposition</option>

							<!-- Low exposition group -->
							<optgroup label="Low exposition">
								<option value="Low exposition|Point of sales"
									<?php echo (isset($item->expositionLevel) && isset($item->expositionDetail) &&
										$item->expositionLevel == 'Low exposition' && $item->expositionDetail == 'Point of sales') ? 'selected' : ''; ?>>
									Point of sales
								</option>
								<option value="Low exposition|Press"
									<?php echo (isset($item->expositionLevel) && isset($item->expositionDetail) &&
										$item->expositionLevel == 'Low exposition' && $item->expositionDetail == 'Press') ? 'selected' : ''; ?>>
									Press
								</option>
							</optgroup>

							<!-- Medium exposition group -->
							<optgroup label="Medium exposition">
								<option value="Medium exposition|Back of the packaging, some social network, digital retailer"
									<?php echo (isset($item->expositionLevel) && isset($item->expositionDetail) &&
										$item->expositionLevel == 'Medium exposition' && $item->expositionDetail == 'Back of the packaging, some social network, digital retailer') ? 'selected' : ''; ?>>
									Back of the packaging, some social network, digital retailer
								</option>
								<option value="Medium exposition|L'Oreal brand website, including a retailing part"
									<?php echo (isset($item->expositionLevel) && isset($item->expositionDetail) &&
										$item->expositionLevel == 'Medium exposition' && $item->expositionDetail == "L'Oreal brand website, including a retailing part") ? 'selected' : ''; ?>>
									L'Oreal brand website, including a retailing part
								</option>
							</optgroup>

							<!-- High exposition group -->
							<optgroup label="High exposition">
								<option value="High exposition|Facing of the packing, TVC, QVC (shopping channel)"
									<?php echo (isset($item->expositionLevel) && isset($item->expositionDetail) &&
										$item->expositionLevel == 'High exposition' && $item->expositionDetail == 'Facing of the packing, TVC, QVC (shopping channel)') ? 'selected' : ''; ?>>
									Facing of the packing, TVC, QVC (shopping channel)
								</option>
							</optgroup>
						</select>

						<!-- Hidden fields to store separated values for backend compatibility -->
						<input type="hidden" name="expositionLevel" id="expositionLevel" value="<?php echo isset($item->expositionLevel) ? $item->expositionLevel : ''; ?>">
						<input type="hidden" name="expositionDetail" id="expositionDetail" value="<?php echo isset($item->expositionDetail) ? $item->expositionDetail : ''; ?>">
					</div>
				</div>

				<!-- Advertisement Type -->
				<div class="form-group">
					<label class="col-sm-3 control-label" for="advertisementType">Advertisement Type</label>
					<div class="col-sm-9">
						<?php $selectedAdType = isset($item) ? $item->advertisementType : 'KV'; ?>
						<select class="form-control" name="advertisementType" id="advertisementType">
							<option value="KV" <?php echo ($selectedAdType == 'KV') ? 'selected' : ''; ?>>KV</option>
							<option value="Clip" <?php echo ($selectedAdType == 'Clip') ? 'selected' : ''; ?>>Clip</option>
						</select>
					</div>
				</div>

				<!-- Timeline -->
				<div class="form-group">
					<label class="col-sm-3 control-label" for="timeline">Timeline</label>
					<div class="col-sm-9">
						<?php
						$timelineValue = '';
						if (isset($item)) {
							if (isset($item->timeline_date)) {
								// Use formatted date if available
								$timelineValue = $item->timeline_date;
							} elseif (isset($item->timeline) && !empty($item->timeline)) {
								// Handle timestamp conversion
								if (is_numeric($item->timeline)) {
									// Convert milliseconds to seconds and format as date
									$timestamp = $item->timeline / 1000;
									$timelineValue = date('Y-m-d', $timestamp);
								} else {
									// Assume it's already a date string
									$timelineValue = $item->timeline;
								}
							}
						}
						?>
						<input class="form-control" id="timeline" name="timeline" value="<?php echo htmlspecialchars($timelineValue); ?>" type="date"/>
					</div>
				</div>

				<?php if($typeForm != "create"):?>
				<!-- Chat Section -->
				<div class="form-group" id="chat-form">
					<label class="col-sm-3 control-label" for="chatMessage">Chat</label>
					<div class="col-sm-9">
						<div class="table-responsive">
							<table class="table table-editable table-hover table-striped table-bordered no-margin">
								<thead class="text-nowrap">
									<tr>
										<th style="display:none">UserId</th>
										<th style="display:none">Email</th>
										<th>FullName</th>
										<th>Time</th>
										<th>Message</th>
										<th>Attachments</th>
									</tr>
								</thead>
								<?php
									$messages = array();
									if(isset($item) && isset($item->messages)){
										$messages = $item->messages;
									}
								?>
								<tbody class="list-chat-messages">
									<?php foreach($messages as $message):?>
									<tr>
										<td class="limit-width message userId" style="display:none"><?php echo $message->userId;?></td>
										<td class="limit-width message email" style="display:none"><?php echo $message->email;?></td>
										<td class="limit-width message fullName"><?php echo $message->fullName;?></td>
										<td class="limit-width message createdTime"><?php echo $message->createdTime;?></td>
										<td class="limit-width message chatMessage"><?php echo $message->message;?></td>
										<td class="limit-width message fileName">
											<?php if(property_exists($message, "attachmentFiles") && $message->attachmentFiles != null):?>
												<div style="display: flex;flex-direction: column;white-space: normal;">
													<?php foreach($message->attachmentFiles as $file_id=>$file_name):?>
														<a id="<?php echo $file_id;?>" class="a-download-file">
															<span class="limit-width">
																-&nbsp;<?php echo $file_name;?>
															</span>
														</a>
													<?php endforeach;?>
												</div>
											<?php endif;?>
										</td>
									</tr>
									<?php endforeach;?>
								</tbody>
							</table>
						</div>
						<br>
						<?php if (in_array(Yii::app()->user->groupName, array("SCI","SCI Manager","SCI Staff")) || (isset($item->status) && $item->status != "COMPLETED")): ?>
							<textarea id="chat-message" class="chat-message form-control input-keyvisual" name="chatMessage" rows="3"></textarea>
						<?php endif; ?>
					</div>

					<?php if (in_array(Yii::app()->user->groupName, array("SCI","SCI Manager","SCI Staff")) || (isset($item->status) && $item->status != "COMPLETED")): ?>
					<!-- Chat Attachments -->
					<label class="col-sm-3 control-label" for="attachments">Attachments</label>
					<div class="col-sm-9">
						<input type="file" id="newAttachmentFiles" class="newAttachmentFiles form-control input-keyvisual inline-block" style="border: none;padding-left: 0;" name="attachments[]" multiple="multiple">
					</div>

					<!-- Chat Add Button -->
					<label class="col-sm-3 control-label" for="add"></label>
					<div class="col-sm-9">
						<button type="button" class="btn btn-primary send-chat-message" style="margin-top: 4px; margin-bottom: 4px;">Add</button>
					</div>
					<?php endif; ?>
				</div>
				<?php endif;?>
			</div>
		</div>
	</div>
	<div class="col-md-6">
		<div class="panel panel-default keyvisual">
			<div class="panel-body">
				<div class="form-group clearfix">
					<label class="col-sm-3 control-label" for="newContentFiles">Content Files</label>
					<div class="col-sm-9">
						<input type="file" class="newContentFiles input-keyvisual inline-block" name="newContentFiles[]" multiple="multiple">
						<?php if(isset($item) && isset($item->contentFiles) && sizeof((array)$item->contentFiles)>0):?>
						<br>
						<div class="upload-file table-responsive">
							<table class="currentContentFiles table table-editable table-hover table-striped table-bordered no-margin">
								<thead class="text-nowrap">
									<tr>
										<th class="limit-width">Filename</th>
										<th>Tools</th>
									</tr>
								</thead>
								<tbody>
									<?php foreach($item->contentFiles as $file_id=>$file_name):?>
									<tr id="<?php echo $file_id;?>">
										<td class="limit-width">
											<?php echo $file_name;?>
										</td>
										<td class="text-center">
											<button class="btn btn-default no-border btn-sm download-file">
												<i class="fa fa-download"></i>
											</button>
											<button class="btn btn-default no-border btn-sm remove-file">
												<i class="fa fa-remove"></i>
											</button>
										</td>
									</tr>
									<?php endforeach;?>
								</tbody>
							</table>
						</div>
						<?php endif;?>
					</div>
				</div>
				<div class="form-group clearfix">
					<label class="col-sm-3 control-label" for="newReferencesFiles">References Files</label>
					<div class="col-sm-9">
						<input type="file" class="newReferencesFiles input-keyvisual inline-block" name="newReferencesFiles[]" multiple="multiple">
						<?php if(isset($item) && isset($item->referencesFiles) && sizeof((array)$item->referencesFiles)>0):?>
						<br>
						<div class="upload-file table-responsive">
							<table class="currentReferencesFiles table table-editable table-hover table-striped table-bordered no-margin">
								<thead class="text-nowrap">
									<tr>
										<th class="limit-width">Filename</th>
										<th>Tools</th>
									</tr>
								</thead>
								<tbody>
									<?php foreach($item->referencesFiles as $file_id=>$file_name):?>
									<tr id="<?php echo $file_id;?>">
										<td class="limit-width">
											<?php echo $file_name;?>
										</td>
										<td class="text-center">
											<button class="btn btn-default no-border btn-sm download-file">
												<i class="fa fa-download"></i>
											</button>
											<button class="btn btn-default no-border btn-sm remove-file">
												<i class="fa fa-remove"></i>
											</button>
										</td>
									</tr>
									<?php endforeach;?>
								</tbody>
							</table>
						</div>
						<?php endif;?>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
